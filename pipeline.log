2025-07-09 17:44:58,964 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:44:58,966 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 0.0, consistency: 0.00)
2025-07-09 17:44:58,969 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:44:58,970 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 0.0, consistency: 0.00)
2025-07-09 17:44:58,970 - DataReader - INFO - Reading txt file: Reference Files for Understanding\contract.txt
2025-07-09 17:44:58,970 - DataReader - INFO - Using encoding: ascii, delimiter: ','
2025-07-09 17:44:59,184 - DataReader - INFO - Successfully read file: 74699 rows x 1 columns
2025-07-09 17:44:59,190 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:44:59,192 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 0.0, consistency: 0.00)
2025-07-09 17:44:59,195 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:44:59,195 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 0.0, consistency: 0.00)
2025-07-09 17:44:59,195 - DataReader - INFO - Reading txt file: Reference Files for Understanding\security.txt
2025-07-09 17:44:59,196 - DataReader - INFO - Using encoding: ascii, delimiter: ','
2025-07-09 17:44:59,273 - DataReader - INFO - Successfully read file: 38876 rows x 1 columns
2025-07-09 17:44:59,282 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:44:59,282 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 150.0, consistency: 1.00)
2025-07-09 17:44:59,285 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:44:59,286 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 150.0, consistency: 1.00)
2025-07-09 17:44:59,286 - DataReader - INFO - Reading csv file: Reference Files for Understanding\BSE_EQD_CONTRACT_02072025.csv
2025-07-09 17:44:59,286 - DataReader - INFO - Using encoding: ascii, delimiter: ','
2025-07-09 17:44:59,559 - DataReader - INFO - Successfully read file: 42326 rows x 150 columns
2025-07-09 17:45:26,352 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:45:26,354 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 0.0, consistency: 0.00)
2025-07-09 17:45:26,357 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:45:26,357 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 0.0, consistency: 0.00)
2025-07-09 17:45:26,357 - DataReader - INFO - Reading txt file: Reference Files for Understanding\contract.txt
2025-07-09 17:45:26,358 - DataReader - INFO - Using encoding: ascii, delimiter: ','
2025-07-09 17:45:26,537 - DataReader - INFO - Successfully read file: 74699 rows x 1 columns
2025-07-09 17:45:26,542 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:45:26,543 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 0.0, consistency: 0.00)
2025-07-09 17:45:26,546 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:45:26,546 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 0.0, consistency: 0.00)
2025-07-09 17:45:26,546 - DataReader - INFO - Reading txt file: Reference Files for Understanding\security.txt
2025-07-09 17:45:26,546 - DataReader - INFO - Using encoding: ascii, delimiter: ','
2025-07-09 17:45:26,640 - DataReader - INFO - Successfully read file: 38876 rows x 1 columns
2025-07-09 17:45:26,650 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:45:26,651 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 150.0, consistency: 1.00)
2025-07-09 17:45:26,655 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:45:26,656 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 150.0, consistency: 1.00)
2025-07-09 17:45:26,656 - DataReader - INFO - Reading csv file: Reference Files for Understanding\BSE_EQD_CONTRACT_02072025.csv
2025-07-09 17:45:26,656 - DataReader - INFO - Using encoding: ascii, delimiter: ','
2025-07-09 17:45:26,939 - DataReader - INFO - Successfully read file: 42326 rows x 150 columns
2025-07-09 17:46:52,029 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:46:52,030 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 69.0, consistency: 1.00)
2025-07-09 17:46:52,033 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:46:52,034 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 69.0, consistency: 1.00)
2025-07-09 17:46:52,034 - DataReader - INFO - Reading txt file: Reference Files for Understanding\contract.txt
2025-07-09 17:46:52,034 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-09 17:46:52,040 - DataReader - ERROR - Error reading file Reference Files for Understanding\contract.txt: Error tokenizing data. C error: Expected 3 fields in line 2, saw 69

2025-07-09 17:46:52,044 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:46:52,044 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 54.0, consistency: 1.00)
2025-07-09 17:46:52,048 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:46:52,048 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 54.0, consistency: 1.00)
2025-07-09 17:46:52,048 - DataReader - INFO - Reading txt file: Reference Files for Understanding\security.txt
2025-07-09 17:46:52,048 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-09 17:46:52,052 - DataReader - ERROR - Error reading file Reference Files for Understanding\security.txt: Error tokenizing data. C error: Expected 3 fields in line 2, saw 54

2025-07-09 17:46:52,055 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:46:52,056 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 150.0, consistency: 1.00)
2025-07-09 17:46:52,059 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:46:52,059 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 150.0, consistency: 1.00)
2025-07-09 17:46:52,060 - DataReader - INFO - Reading csv file: Reference Files for Understanding\BSE_EQD_CONTRACT_02072025.csv
2025-07-09 17:46:52,060 - DataReader - INFO - Using encoding: ascii, delimiter: ','
2025-07-09 17:46:52,355 - DataReader - INFO - Successfully read file: 42326 rows x 150 columns
2025-07-09 17:47:31,374 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:47:31,376 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 69.0, consistency: 1.00)
2025-07-09 17:47:31,379 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:47:31,379 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 69.0, consistency: 1.00)
2025-07-09 17:47:31,379 - DataReader - INFO - Reading txt file: Reference Files for Understanding\contract.txt
2025-07-09 17:47:31,379 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-09 17:47:31,386 - DataReader - WARNING - Inconsistent columns detected, using error handling
2025-07-09 17:47:31,568 - DataReader - INFO - Successfully read file: 1 rows x 3 columns
2025-07-09 17:47:31,574 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:47:31,574 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 54.0, consistency: 1.00)
2025-07-09 17:47:31,577 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:47:31,578 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 54.0, consistency: 1.00)
2025-07-09 17:47:31,578 - DataReader - INFO - Reading txt file: Reference Files for Understanding\security.txt
2025-07-09 17:47:31,578 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-09 17:47:31,583 - DataReader - WARNING - Inconsistent columns detected, using error handling
2025-07-09 17:47:31,680 - DataReader - INFO - Successfully read file: 1 rows x 3 columns
2025-07-09 17:47:31,686 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:47:31,687 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 150.0, consistency: 1.00)
2025-07-09 17:47:31,690 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:47:31,691 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 150.0, consistency: 1.00)
2025-07-09 17:47:31,691 - DataReader - INFO - Reading csv file: Reference Files for Understanding\BSE_EQD_CONTRACT_02072025.csv
2025-07-09 17:47:31,691 - DataReader - INFO - Using encoding: ascii, delimiter: ','
2025-07-09 17:47:31,979 - DataReader - INFO - Successfully read file: 42326 rows x 150 columns
2025-07-09 17:48:23,975 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:48:23,977 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 69.0, consistency: 1.00)
2025-07-09 17:48:23,980 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:48:23,980 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 69.0, consistency: 1.00)
2025-07-09 17:48:23,980 - DataReader - INFO - Reading txt file: Reference Files for Understanding\contract.txt
2025-07-09 17:48:23,980 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-09 17:48:24,422 - DataReader - INFO - Read 74699 rows with 69 columns (padded)
2025-07-09 17:48:24,454 - DataReader - INFO - Successfully read file: 74699 rows x 69 columns
2025-07-09 17:48:24,466 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:48:24,466 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 54.0, consistency: 1.00)
2025-07-09 17:48:24,470 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:48:24,470 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 54.0, consistency: 1.00)
2025-07-09 17:48:24,471 - DataReader - INFO - Reading txt file: Reference Files for Understanding\security.txt
2025-07-09 17:48:24,471 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-09 17:48:24,638 - DataReader - INFO - Read 38876 rows with 54 columns (padded)
2025-07-09 17:48:24,649 - DataReader - INFO - Successfully read file: 38876 rows x 54 columns
2025-07-09 17:48:24,722 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:48:24,723 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 150.0, consistency: 1.00)
2025-07-09 17:48:24,726 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-09 17:48:24,726 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 150.0, consistency: 1.00)
2025-07-09 17:48:24,727 - DataReader - INFO - Reading csv file: Reference Files for Understanding\BSE_EQD_CONTRACT_02072025.csv
2025-07-09 17:48:24,727 - DataReader - INFO - Using encoding: ascii, delimiter: ','
2025-07-09 17:48:25,013 - DataReader - INFO - Successfully read file: 42326 rows x 150 columns
2025-07-10 12:17:55,229 - MasterExcelReader - INFO - Read 69 transformation rules for SR 1
2025-07-10 12:17:55,253 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-10 12:17:55,255 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 69.0, consistency: 1.00)
2025-07-10 12:17:55,256 - DataReader - INFO - Reading txt file: Reference Files for Understanding\contract.txt
2025-07-10 12:17:55,256 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-10 12:17:55,707 - DataReader - INFO - Read 74699 rows with 69 columns (padded)
2025-07-10 12:17:55,734 - DataReader - INFO - Successfully read file: 74699 rows x 69 columns
2025-07-10 12:17:57,079 - TransformationEngine - INFO - Applied transformations: 8 columns transformed, 54 columns dropped
2025-07-10 12:20:10,676 - DataProcessingPipeline - INFO - Processing file: contract.txt (SR 1)
2025-07-10 12:20:11,315 - MasterExcelReader - INFO - Read 69 transformation rules for SR 1
2025-07-10 12:20:11,318 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-10 12:20:11,320 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 69.0, consistency: 1.00)
2025-07-10 12:20:11,320 - DataReader - INFO - Reading txt file: Reference Files for Understanding\contract.txt
2025-07-10 12:20:11,321 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-10 12:20:11,752 - DataReader - INFO - Read 74699 rows with 69 columns (padded)
2025-07-10 12:20:11,781 - DataReader - INFO - Successfully read file: 74699 rows x 69 columns
2025-07-10 12:20:12,450 - TransformationEngine - INFO - Applied transformations: 8 columns transformed, 54 columns dropped
2025-07-10 12:20:24,133 - OutputGenerator - INFO - Generated output file: test_output\contract_cleaned_20250710_122012.xlsx
2025-07-10 12:20:24,134 - DataProcessingPipeline - INFO - Successfully processed contract.txt
2025-07-10 12:20:24,215 - MasterExcelReader - INFO - Read 3 files from Sheet1
2025-07-10 12:20:24,216 - DataProcessingPipeline - INFO - Processing file: contract.txt (SR 1)
2025-07-10 12:20:25,205 - MasterExcelReader - INFO - Read 69 transformation rules for SR 1
2025-07-10 12:20:25,206 - DataProcessingPipeline - ERROR - Error processing contract.txt: File not found: C:\Contracts\10.06.2025\CONTRACT & SECURITY FILE\contract.txt
2025-07-10 12:23:31,147 - MasterExcelReader - INFO - Read 3 files from Sheet1
2025-07-10 12:23:50,723 - MasterExcelReader - INFO - Read 3 files from Sheet1
2025-07-10 12:34:51,357 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 3.0, consistency: 1.00)
2025-07-10 12:34:51,365 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 3.0, consistency: 1.00)
2025-07-10 12:34:51,373 - FileDetector - INFO - Detected delimiter: '	' (avg cols: 3.0, consistency: 1.00)
2025-07-10 12:34:51,383 - FileDetector - INFO - Detected encoding: utf-8 (confidence: 0.75)
2025-07-10 12:34:51,386 - FileDetector - WARNING - Unknown file extension 'unknown', treating as TXT
2025-07-10 12:34:51,396 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-10 12:34:51,398 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 3.0, consistency: 1.00)
2025-07-10 12:34:51,398 - DataReader - INFO - Reading csv file: C:\Users\<USER>\AppData\Local\Temp\tmpha54426p\test.csv
2025-07-10 12:34:51,398 - DataReader - INFO - Using encoding: ascii, delimiter: ','
2025-07-10 12:34:51,436 - DataReader - INFO - Successfully read file: 4 rows x 3 columns
2025-07-10 12:34:51,445 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-10 12:34:51,446 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 2.7, consistency: 0.25)
2025-07-10 12:34:51,446 - DataReader - INFO - Reading txt file: C:\Users\<USER>\AppData\Local\Temp\tmpcoixahhy\test_inconsistent.txt
2025-07-10 12:34:51,446 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-10 12:34:51,446 - DataReader - INFO - Read 3 rows with 4 columns (padded)
2025-07-10 12:34:51,447 - DataReader - INFO - Successfully read file: 3 rows x 4 columns
2025-07-10 12:34:51,449 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-10 12:34:51,450 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 3.0, consistency: 1.00)
2025-07-10 12:34:51,450 - DataReader - INFO - Reading txt file: C:\Users\<USER>\AppData\Local\Temp\tmpg4s7d16f\test.txt
2025-07-10 12:34:51,450 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-10 12:34:51,450 - DataReader - INFO - Read 3 rows with 3 columns (padded)
2025-07-10 12:34:51,451 - DataReader - INFO - Successfully read file: 3 rows x 3 columns
2025-07-10 12:55:47,887 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 3.0, consistency: 1.00)
2025-07-10 12:55:47,889 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 3.0, consistency: 1.00)
2025-07-10 12:55:47,891 - FileDetector - INFO - Detected delimiter: '	' (avg cols: 3.0, consistency: 1.00)
2025-07-10 12:55:47,894 - FileDetector - INFO - Detected encoding: utf-8 (confidence: 0.75)
2025-07-10 12:55:47,897 - FileDetector - WARNING - Unknown file extension 'unknown', treating as TXT
2025-07-10 12:55:47,901 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-10 12:55:47,902 - FileDetector - INFO - Detected delimiter: ',' (avg cols: 3.0, consistency: 1.00)
2025-07-10 12:55:47,902 - DataReader - INFO - Reading csv file: C:\Users\<USER>\AppData\Local\Temp\tmp6uxllt_3\test.csv
2025-07-10 12:55:47,902 - DataReader - INFO - Using encoding: ascii, delimiter: ','
2025-07-10 12:55:47,908 - DataReader - INFO - Successfully read file: 4 rows x 3 columns
2025-07-10 12:55:47,911 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-10 12:55:47,912 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 2.7, consistency: 0.25)
2025-07-10 12:55:47,912 - DataReader - INFO - Reading txt file: C:\Users\<USER>\AppData\Local\Temp\tmpzix0u3c4\test_inconsistent.txt
2025-07-10 12:55:47,912 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-10 12:55:47,913 - DataReader - INFO - Read 3 rows with 4 columns (padded)
2025-07-10 12:55:47,913 - DataReader - INFO - Successfully read file: 3 rows x 4 columns
2025-07-10 12:55:47,916 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-10 12:55:47,917 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 3.0, consistency: 1.00)
2025-07-10 12:55:47,917 - DataReader - INFO - Reading txt file: C:\Users\<USER>\AppData\Local\Temp\tmpnl6v6dtj\test.txt
2025-07-10 12:55:47,917 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-10 12:55:47,918 - DataReader - INFO - Read 3 rows with 3 columns (padded)
2025-07-10 12:55:47,918 - DataReader - INFO - Successfully read file: 3 rows x 3 columns
2025-07-10 17:00:26,743 - TransformationEngine - INFO - Applied transformations: 2 columns transformed, 1 columns dropped
2025-07-10 17:00:26,743 - TransformationEngine - INFO - Final columns: ['Token', 'Instrument', 'Symbol', 'Series', 'Option_Type', 'Expiry_Date', 'Strike_Price', 'Call_Put']
2025-07-10 17:00:42,803 - DataProcessingPipeline - INFO - Processing file: contract.txt (SR 1)
2025-07-10 17:00:43,499 - MasterExcelReader - INFO - Read 69 transformation rules for SR 1
2025-07-10 17:00:43,504 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-10 17:00:43,506 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 69.0, consistency: 1.00)
2025-07-10 17:00:43,507 - DataReader - INFO - Reading txt file: Reference Files for Understanding\contract.txt
2025-07-10 17:00:43,507 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-10 17:00:43,990 - DataReader - INFO - Read 74699 rows with 69 columns (padded)
2025-07-10 17:00:44,023 - DataReader - INFO - Successfully read file: 74699 rows x 69 columns
2025-07-10 17:00:44,773 - TransformationEngine - INFO - Applied transformations: 8 columns transformed, 54 columns dropped
2025-07-10 17:00:44,773 - TransformationEngine - INFO - Final columns: ['Token', 'instrument_name', 'Symbol', 'Series', 'expiry_date', 'strike_price', 'option_type', 'board_lot_quantity', 'volume_freeze_qty', 'date_time.record_date', 'date_time.no_delivery_dates.start', 'date_time.no_delivery_dates.end', 'date_time.ex_date', 'stock.book_closure_date_end', 'base_price']
2025-07-10 17:00:56,598 - OutputGenerator - INFO - Generated output file: test_output\contract_cleaned_20250710_170044.xlsx
2025-07-10 17:00:56,599 - DataProcessingPipeline - INFO - Successfully processed contract.txt
2025-07-10 17:00:56,692 - MasterExcelReader - INFO - Read 3 files from Sheet1
2025-07-10 17:00:56,693 - DataProcessingPipeline - INFO - Processing file: contract.txt (SR 1)
2025-07-10 17:00:57,814 - MasterExcelReader - INFO - Read 69 transformation rules for SR 1
2025-07-10 17:00:57,819 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-10 17:00:57,819 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 69.0, consistency: 1.00)
2025-07-10 17:00:57,820 - DataReader - INFO - Reading txt file: C:\Contracts\10.06.2025\CONTRACT & SECURITY FILE\contract.txt
2025-07-10 17:00:57,820 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-10 17:00:58,233 - DataReader - INFO - Read 74699 rows with 69 columns (padded)
2025-07-10 17:00:58,263 - DataReader - INFO - Successfully read file: 74699 rows x 69 columns
2025-07-10 17:00:59,008 - TransformationEngine - INFO - Applied transformations: 8 columns transformed, 54 columns dropped
2025-07-10 17:00:59,009 - TransformationEngine - INFO - Final columns: ['Token', 'instrument_name', 'Symbol', 'Series', 'expiry_date', 'strike_price', 'option_type', 'board_lot_quantity', 'volume_freeze_qty', 'date_time.record_date', 'date_time.no_delivery_dates.start', 'date_time.no_delivery_dates.end', 'date_time.ex_date', 'stock.book_closure_date_end', 'base_price']
2025-07-10 17:01:10,783 - OutputGenerator - INFO - Generated output file: test_output\contract_cleaned_20250710_170059.xlsx
2025-07-10 17:01:10,784 - DataProcessingPipeline - INFO - Successfully processed contract.txt
2025-07-11 12:08:27,783 - OutputGenerator - INFO - Replaced blank values with '-' in final output
2025-07-11 12:08:27,818 - OutputGenerator - INFO - Generated output file: test_output\test_cleaned_20250711_120827.xlsx
2025-07-11 12:08:27,826 - TransformationEngine - INFO - Applied format conversions: text_col -> text, number_col -> number
2025-07-11 12:08:27,827 - TransformationEngine - INFO - Applied transformations: 0 columns transformed, 0 columns dropped
2025-07-11 12:08:27,827 - TransformationEngine - INFO - Final columns: ['text_col', 'number_col']
2025-07-11 12:08:38,083 - DataProcessingPipeline - INFO - Starting data processing pipeline
2025-07-11 12:08:38,244 - MasterExcelReader - INFO - Read 14 files from Sheet1
2025-07-11 12:08:38,244 - DataProcessingPipeline - INFO - Found 14 files to process
2025-07-11 12:08:38,244 - DataProcessingPipeline - INFO - Processing file: contract.txt (SR 1)
2025-07-11 12:08:38,899 - MasterExcelReader - INFO - Read 69 transformation rules for SR 1
2025-07-11 12:08:38,915 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-11 12:08:38,917 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 69.0, consistency: 1.00)
2025-07-11 12:08:38,918 - DataReader - INFO - Reading txt file: C:\Contracts\10.06.2025\CONTRACT & SECURITY FILE\contract.txt
2025-07-11 12:08:38,918 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-11 12:08:39,425 - DataReader - INFO - Read 78932 rows with 69 columns (padded)
2025-07-11 12:08:39,458 - DataReader - INFO - Successfully read file: 78932 rows x 69 columns
2025-07-11 12:08:43,561 - TransformationEngine - INFO - Applied format conversions: Token -> number, instrument_name -> text, Symbol -> text, Series -> text, expiry_date -> number, strike_price -> number, option_type -> text, board_lot_quantity -> number, volume_freeze_qty -> number, date_time.record_date -> number, date_time.no_delivery_dates.start -> number, date_time.no_delivery_dates.end -> number, date_time.ex_date -> number, stock.book_closure_date_end -> number, base_price -> number
2025-07-11 12:08:43,561 - TransformationEngine - INFO - Applied transformations: 8 columns transformed, 54 columns dropped
2025-07-11 12:08:43,561 - TransformationEngine - INFO - Final columns: ['Token', 'instrument_name', 'Symbol', 'Series', 'expiry_date', 'strike_price', 'option_type', 'board_lot_quantity', 'volume_freeze_qty', 'date_time.record_date', 'date_time.no_delivery_dates.start', 'date_time.no_delivery_dates.end', 'date_time.ex_date', 'stock.book_closure_date_end', 'base_price']
2025-07-11 12:08:44,295 - OutputGenerator - INFO - Replaced blank values with '-' in final output
2025-07-11 12:35:09,655 - MasterExcelReader - INFO - Read 14 files from Sheet1
2025-07-11 12:35:10,316 - MasterExcelReader - INFO - Read 69 transformation rules for SR 1
2025-07-11 12:35:10,613 - MasterExcelReader - INFO - Read 49 transformation rules for SR 10
2025-07-11 12:35:10,622 - MasterExcelReader - WARNING - Sheet 'Sr. No. 11' not found
2025-07-11 12:35:10,630 - OutputGenerator - INFO - Replaced blank values with '-' in final output
2025-07-11 12:35:10,637 - OutputGenerator - INFO - Applied Excel cell formatting for 3 columns
2025-07-11 12:35:10,666 - OutputGenerator - INFO - Generated output file: test_output\format_test_cleaned_20250711_123510.xlsx
2025-07-11 12:35:10,839 - MasterExcelReader - INFO - Read 14 files from Sheet1
2025-07-11 12:35:10,847 - MasterExcelReader - WARNING - Sheet 'Sr. No. 11' not found
2025-07-11 12:35:10,856 - MasterExcelReader - WARNING - Sheet 'Sr. No. 5' not found
2025-07-11 12:35:10,865 - MasterExcelReader - WARNING - Sheet 'Sr. No. 4' not found
2025-07-11 12:35:10,874 - MasterExcelReader - WARNING - Sheet 'Sr. No. 7' not found
2025-07-11 12:35:10,882 - MasterExcelReader - WARNING - Sheet 'Sr. No. 8' not found
2025-07-11 12:35:10,890 - MasterExcelReader - WARNING - Sheet 'Sr. No. 13' not found
2025-07-11 12:35:10,898 - MasterExcelReader - WARNING - Sheet 'Sr. No. 6' not found
2025-07-11 12:35:10,909 - MasterExcelReader - WARNING - Sheet 'Sr. No. 14' not found
2025-07-11 12:35:11,551 - MasterExcelReader - INFO - Read 69 transformation rules for SR 1
2025-07-11 12:35:11,982 - MasterExcelReader - INFO - Read 54 transformation rules for SR 2
2025-07-11 12:35:11,991 - MasterExcelReader - WARNING - Sheet 'Sr. No. 12' not found
2025-07-11 12:35:11,999 - MasterExcelReader - WARNING - Sheet 'Sr. No. 3' not found
2025-07-11 12:35:12,264 - MasterExcelReader - INFO - Read 49 transformation rules for SR 10
2025-07-11 12:35:12,272 - MasterExcelReader - WARNING - Sheet 'Sr. No. 9' not found
2025-07-11 14:14:28,965 - DataProcessingPipeline - INFO - Starting data processing pipeline
2025-07-11 14:14:29,105 - MasterExcelReader - INFO - Read 14 files from Sheet1
2025-07-11 14:14:29,106 - DataProcessingPipeline - INFO - Found 14 files to process
2025-07-11 14:14:29,106 - DataProcessingPipeline - INFO - Processing file: contract.txt (SR 1)
2025-07-11 14:14:30,115 - MasterExcelReader - INFO - Read 69 transformation rules for SR 1
2025-07-11 14:14:30,130 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-11 14:14:30,134 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 69.0, consistency: 1.00)
2025-07-11 14:14:30,134 - DataReader - INFO - Reading txt file: C:\Contracts\10.06.2025\CONTRACT & SECURITY FILE\contract.txt
2025-07-11 14:14:30,134 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-11 14:14:30,697 - DataReader - INFO - Read 74699 rows with 69 columns (padded)
2025-07-11 14:14:30,728 - DataReader - INFO - Successfully read file: 74699 rows x 69 columns
2025-07-11 14:14:39,130 - TransformationEngine - INFO - Applied format conversions: Token -> number, instrument_name -> text, Symbol -> text, Series -> text, expiry_date -> number, strike_price -> number, option_type -> text, board_lot_quantity -> number, volume_freeze_qty -> number, date_time.record_date -> number, date_time.no_delivery_dates.start -> number, date_time.no_delivery_dates.end -> number, date_time.ex_date -> number, stock.book_closure_date_end -> number, base_price -> number
2025-07-11 14:14:39,131 - TransformationEngine - INFO - Applied transformations: 8 columns transformed, 54 columns dropped
2025-07-11 14:14:39,131 - TransformationEngine - INFO - Final columns: ['Token', 'instrument_name', 'Symbol', 'Series', 'expiry_date', 'strike_price', 'option_type', 'board_lot_quantity', 'volume_freeze_qty', 'date_time.record_date', 'date_time.no_delivery_dates.start', 'date_time.no_delivery_dates.end', 'date_time.ex_date', 'stock.book_closure_date_end', 'base_price']
2025-07-11 14:14:40,052 - OutputGenerator - INFO - Replaced blank values with '-' in final output
2025-07-11 14:15:26,517 - OutputGenerator - INFO - Applied Excel cell formatting for 15 columns
2025-07-11 14:15:40,702 - OutputGenerator - INFO - Generated output file: output\contract_cleaned_20250711_141440.xlsx
2025-07-11 14:15:40,713 - DataProcessingPipeline - INFO - Successfully processed contract.txt
2025-07-11 14:15:40,783 - DataProcessingPipeline - INFO - Processing file: security.txt (SR 2)
2025-07-11 14:15:41,354 - MasterExcelReader - INFO - Read 54 transformation rules for SR 2
2025-07-11 14:15:41,369 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-11 14:15:41,369 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 54.0, consistency: 1.00)
2025-07-11 14:15:41,369 - DataReader - INFO - Reading txt file: C:\Contracts\10.06.2025\CONTRACT & SECURITY FILE\security.txt
2025-07-11 14:15:41,370 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-11 14:15:41,576 - DataReader - INFO - Read 38876 rows with 54 columns (padded)
2025-07-11 14:15:41,587 - DataReader - INFO - Successfully read file: 38876 rows x 54 columns
2025-07-11 14:15:48,603 - TransformationEngine - INFO - Applied format conversions: Token -> number, Symbol -> text, Series -> text, IssuedCapital -> number, BoardLotQuantity -> number, TickSize -> number, Name -> text, FreezePercent -> number, ListingDate -> number, ExDate -> number, RecordDate -> number, NoDeliveryStartDate -> number, NoDeliveryEndDate -> number, ParticipateInMktIndex -> number, BookClosureStartDate -> number, BookClosureEndDate -> number, Remark -> text, LocalLDBUpdateDateTime -> number, FaceValue -> number, ISIN No. -> text
2025-07-11 14:15:48,603 - TransformationEngine - INFO - Applied transformations: 13 columns transformed, 25 columns dropped
2025-07-11 14:15:48,603 - TransformationEngine - INFO - Final columns: ['Token', 'Symbol', 'Series', 'IssuedCapital', 'BoardLotQuantity', 'TickSize', 'Name', 'SurvInd', 'IssueStartDate', 'IssueIPDate', 'FreezePercent', 'ListingDate', 'ExDate', 'RecordDate', 'NoDeliveryStartDate', 'NoDeliveryEndDate', 'ParticipateInMktIndex', 'BookClosureStartDate', 'BookClosureEndDate', 'Dividend', 'Rights', 'Bonus', 'Interest', 'AGM', 'EGM', 'Remark', 'LocalLDBUpdateDateTime', 'FaceValue', 'ISIN No.']
2025-07-11 14:15:49,466 - OutputGenerator - INFO - Replaced blank values with '-' in final output
2025-07-11 14:16:29,940 - OutputGenerator - WARNING - Error applying Excel cell formatting: [2 is not a valid coordinate or range
2025-07-11 14:16:44,180 - OutputGenerator - INFO - Generated output file: output\security_cleaned_20250711_141549.xlsx
2025-07-11 14:16:44,190 - DataProcessingPipeline - INFO - Successfully processed security.txt
2025-07-11 14:16:44,227 - DataProcessingPipeline - INFO - Processing file: C_VAR1_DDMMYYYY_1.DAT (SR 3)
2025-07-11 14:16:44,239 - MasterExcelReader - WARNING - Sheet 'Sr. No. 3' not found
2025-07-11 14:16:44,239 - DataProcessingPipeline - INFO - Processing file: ael.DDMMYYYY.csv (SR 4)
2025-07-11 14:16:44,251 - MasterExcelReader - WARNING - Sheet 'Sr. No. 4' not found
2025-07-11 14:16:44,252 - DataProcessingPipeline - INFO - Processing file: DailyMargin_20250609063604.csv (SR 5)
2025-07-11 14:16:44,263 - MasterExcelReader - WARNING - Sheet 'Sr. No. 5' not found
2025-07-11 14:16:44,263 - DataProcessingPipeline - INFO - Processing file: BhavCopy_NSE_FO_0_0_0_YYYYMMDD_F_0000 (SR 6)
2025-07-11 14:16:44,275 - MasterExcelReader - WARNING - Sheet 'Sr. No. 6' not found
2025-07-11 14:16:44,275 - DataProcessingPipeline - INFO - Processing file: NSE_FO_SosScheme.csv (SR 7)
2025-07-11 14:16:44,287 - MasterExcelReader - WARNING - Sheet 'Sr. No. 7' not found
2025-07-11 14:16:44,288 - DataProcessingPipeline - INFO - Processing file: sec_bhavdata_full_10062025.csv (SR 8)
2025-07-11 14:16:44,300 - MasterExcelReader - WARNING - Sheet 'Sr. No. 8' not found
2025-07-11 14:16:44,300 - DataProcessingPipeline - INFO - Processing file: BSE_EQD_CONTRACT_09062025.csv (SR 9)
2025-07-11 14:16:44,312 - MasterExcelReader - WARNING - Sheet 'Sr. No. 9' not found
2025-07-11 14:16:44,313 - DataProcessingPipeline - INFO - Processing file: security_slb.txt (SR 10)
2025-07-11 14:16:44,713 - MasterExcelReader - INFO - Read 49 transformation rules for SR 10
2025-07-11 14:16:44,729 - FileDetector - INFO - Detected encoding: ascii (confidence: 1.00)
2025-07-11 14:16:44,729 - FileDetector - INFO - Detected delimiter: '|' (avg cols: 49.0, consistency: 1.00)
2025-07-11 14:16:44,729 - DataReader - INFO - Reading txt file: C:\Contracts\10.06.2025\CONTRACT & SECURITY FILE\security_slb.txt
2025-07-11 14:16:44,730 - DataReader - INFO - Using encoding: ascii, delimiter: '|'
2025-07-11 14:16:45,199 - DataReader - INFO - Read 93782 rows with 49 columns (padded)
2025-07-11 14:16:45,223 - DataReader - INFO - Successfully read file: 93782 rows x 49 columns
2025-07-11 14:16:49,060 - TransformationEngine - INFO - Applied format conversions: Symbol -> text, PermittedToTrade -> text, ExDate -> number, Record Date -> number
2025-07-11 14:16:49,061 - TransformationEngine - INFO - Applied transformations: 3 columns transformed, 36 columns dropped
2025-07-11 14:16:49,061 - TransformationEngine - INFO - Final columns: ['Token', 'Symbol', 'Series', 'PermittedToTrade', 'Eligibility', 'Eligibility', 'Eligibility', 'Eligibility', 'ExDate', 'Record Date', 'WarningPercent', 'Remark', 'FaceValue', 'ISIN Number', 'AllowRecall', 'AllowRepay']
2025-07-11 14:16:50,194 - OutputGenerator - INFO - Replaced blank values with '-' in final output
2025-07-11 14:17:26,712 - OutputGenerator - INFO - Applied Excel cell formatting for 8 columns
2025-07-11 14:17:45,165 - OutputGenerator - INFO - Generated output file: output\security_slb_cleaned_20250711_141650.xlsx
2025-07-11 14:17:45,176 - DataProcessingPipeline - INFO - Successfully processed security_slb.txt
2025-07-11 14:17:45,233 - DataProcessingPipeline - INFO - Processing file: combineoi_09062025.csv (SR 11)
2025-07-11 14:17:45,244 - MasterExcelReader - WARNING - Sheet 'Sr. No. 11' not found
2025-07-11 14:17:45,244 - DataProcessingPipeline - INFO - Processing file: BSE_EQ_SCRIP_09062025.csv (SR 12)
2025-07-11 14:17:45,257 - MasterExcelReader - WARNING - Sheet 'Sr. No. 12' not found
2025-07-11 14:17:45,257 - DataProcessingPipeline - INFO - Processing file: ind_close_all_17062025.csv (SR 13)
2025-07-11 14:17:45,268 - MasterExcelReader - WARNING - Sheet 'Sr. No. 13' not found
2025-07-11 14:17:45,270 - DataProcessingPipeline - INFO - Processing file: SLBM_BC_19062025.DAT (SR 14)
2025-07-11 14:17:45,282 - MasterExcelReader - WARNING - Sheet 'Sr. No. 14' not found
2025-07-11 14:17:45,314 - DataProcessingPipeline - INFO - Generated summary report: output\processing_summary_20250711_141745.xlsx
2025-07-11 14:17:45,315 - DataProcessingPipeline - INFO - Pipeline processing completed
