#!/usr/bin/env python3
"""
Test script for the data formatting functionality

This script tests the post-processing formatting capabilities by:
1. Reading format specifications from BRD_Automation_RAW.xlsx
2. Testing format mapping functionality
3. Applying formatting to sample Excel files
"""

import sys
import logging
from pathlib import Path
import pandas as pd
from data_formatter import FormatMappingReader, ExcelFormatter, PostProcessingManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_format_mapping_reader():
    """Test the FormatMappingReader functionality"""
    print("=" * 60)
    print("TESTING FORMAT MAPPING READER")
    print("=" * 60)
    
    try:
        master_file = "BRD_Automation_RAW.xlsx"
        if not Path(master_file).exists():
            print(f"Error: Master file '{master_file}' not found")
            return False
        
        reader = FormatMappingReader(master_file)
        format_mappings = reader.read_all_format_specifications()
        
        print(f"Successfully read format specifications for {len(format_mappings)} Sr.no. sheets")
        
        # Display summary of format types found
        all_format_types = set()
        total_rules = 0
        
        for sr_number, rules in format_mappings.items():
            print(f"\nSr.no. {sr_number}: {len(rules)} rules")
            
            format_types_in_sr = set()
            required_count = 0
            
            for rule in rules:
                total_rules += 1
                if rule.format_type and rule.format_type != "-":
                    all_format_types.add(rule.format_type)
                    format_types_in_sr.add(rule.format_type)
                if rule.required:
                    required_count += 1
                    
                # Show calculation formulas if any
                if rule.calculation_formula:
                    print(f"  Calculation: {rule.header_name} = {rule.calculation_formula}")
            
            print(f"  Required columns: {required_count}")
            print(f"  Format types: {', '.join(sorted(format_types_in_sr)) if format_types_in_sr else 'None'}")
        
        print(f"\nSUMMARY:")
        print(f"Total Sr.no. sheets: {len(format_mappings)}")
        print(f"Total format rules: {total_rules}")
        print(f"Unique format types found: {', '.join(sorted(all_format_types))}")
        
        return True
        
    except Exception as e:
        print(f"Error testing format mapping reader: {e}")
        return False

def test_excel_formatter():
    """Test the ExcelFormatter functionality"""
    print("\n" + "=" * 60)
    print("TESTING EXCEL FORMATTER")
    print("=" * 60)
    
    try:
        # Look for existing Excel files in output directory
        output_dir = Path("output")
        if not output_dir.exists():
            print("Output directory not found. Creating sample Excel file for testing...")
            return create_and_test_sample_file()
        
        excel_files = list(output_dir.glob("*.xlsx"))
        if not excel_files:
            print("No Excel files found in output directory. Creating sample Excel file for testing...")
            return create_and_test_sample_file()
        
        # Test with the first Excel file found
        test_file = excel_files[0]
        print(f"Testing with file: {test_file}")
        
        # Read format specifications
        master_file = "BRD_Automation_RAW.xlsx"
        reader = FormatMappingReader(master_file)
        format_mappings = reader.read_all_format_specifications()
        
        if not format_mappings:
            print("No format mappings found")
            return False
        
        # Use the first available format rules for testing
        sr_number = list(format_mappings.keys())[0]
        format_rules = format_mappings[sr_number]
        
        print(f"Using format rules from Sr.no. {sr_number} ({len(format_rules)} rules)")
        
        # Apply formatting
        formatter = ExcelFormatter()
        result = formatter.format_excel_file(str(test_file), format_rules)
        
        print(f"\nFormatting Result:")
        print(f"Status: {result.status}")
        print(f"Columns formatted: {', '.join(result.columns_formatted) if result.columns_formatted else 'None'}")
        if result.errors:
            print(f"Errors: {'; '.join(result.errors)}")
        
        return result.status in ["Success", "Partial"]
        
    except Exception as e:
        print(f"Error testing Excel formatter: {e}")
        return False

def create_and_test_sample_file():
    """Create a sample Excel file and test formatting"""
    try:
        # Create sample data that matches Sr.no. 5 format
        sample_data = {
            'Date': ['2024-07-11', '2024-07-12', '2024-07-13'],
            'Symbol': ['AAPL', 'GOOGL', 'MSFT'],
            'Initial Margin(%)': ['15.5', '12.3', '18.7'],
            'Total Margin(%)': ['20.0', '15.5', '22.1'],
            'Daily Volatility': ['0.025', '0.031', '0.019']
        }
        
        df = pd.DataFrame(sample_data)
        
        # Create output directory if it doesn't exist
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)
        
        # Save sample file
        sample_file = output_dir / "sample_test_file.xlsx"
        df.to_excel(sample_file, index=False)
        
        print(f"Created sample file: {sample_file}")
        
        # Read format specifications for Sr.no. 5
        master_file = "BRD_Automation_RAW.xlsx"
        reader = FormatMappingReader(master_file)
        format_mappings = reader.read_all_format_specifications()
        
        if "5" in format_mappings:
            format_rules = format_mappings["5"]
            print(f"Using Sr.no. 5 format rules ({len(format_rules)} rules)")
            
            # Apply formatting
            formatter = ExcelFormatter()
            result = formatter.format_excel_file(str(sample_file), format_rules)
            
            print(f"\nFormatting Result:")
            print(f"Status: {result.status}")
            print(f"Columns formatted: {', '.join(result.columns_formatted) if result.columns_formatted else 'None'}")
            if result.errors:
                print(f"Errors: {'; '.join(result.errors)}")
            
            return result.status in ["Success", "Partial"]
        else:
            print("Sr.no. 5 format rules not found")
            return False
            
    except Exception as e:
        print(f"Error creating and testing sample file: {e}")
        return False

def test_post_processing_manager():
    """Test the PostProcessingManager functionality"""
    print("\n" + "=" * 60)
    print("TESTING POST-PROCESSING MANAGER")
    print("=" * 60)
    
    try:
        master_file = "BRD_Automation_RAW.xlsx"
        output_dir = "output"
        
        manager = PostProcessingManager(master_file, output_dir)
        
        # Test format all files
        print("Testing format_all_output_files()...")
        results = manager.format_all_output_files()
        
        print(f"\nPost-processing Results:")
        print(f"Total files processed: {len(results)}")
        
        for status in ["Success", "Partial", "Failed", "Skipped"]:
            count = len([r for r in results if r.status == status])
            if count > 0:
                print(f"{status}: {count}")
        
        # Show detailed results
        print(f"\nDetailed Results:")
        for result in results:
            status_symbol = {"Success": "✓", "Partial": "⚠", "Failed": "✗", "Skipped": "○"}
            symbol = status_symbol.get(result.status, "?")
            filename = Path(result.file_path).name
            print(f"{symbol} {filename} (SR {result.sr_number}) - {result.status}")
            
            if result.columns_formatted:
                print(f"    Formatted: {', '.join(result.columns_formatted)}")
            if result.errors:
                for error in result.errors:
                    print(f"    Error: {error}")
        
        successful_count = len([r for r in results if r.status in ["Success", "Partial"]])
        return successful_count > 0
        
    except Exception as e:
        print(f"Error testing post-processing manager: {e}")
        return False

def main():
    """Main test function"""
    print("DATA FORMATTING FUNCTIONALITY TEST")
    print("=" * 60)
    
    tests = [
        ("Format Mapping Reader", test_format_mapping_reader),
        ("Excel Formatter", test_excel_formatter),
        ("Post-Processing Manager", test_post_processing_manager)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\nRunning test: {test_name}")
            success = test_func()
            results.append((test_name, success))
            print(f"Test {test_name}: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            print(f"Test {test_name}: FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "PASSED" if success else "FAILED"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    return 0 if passed == len(results) else 1

if __name__ == "__main__":
    sys.exit(main())
