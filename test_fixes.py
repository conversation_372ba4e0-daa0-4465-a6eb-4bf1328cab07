#!/usr/bin/env python3
"""
Test script to verify all four fixes are working correctly
"""

import pandas as pd
from datetime import datetime
from data_pipeline import TransformationEngine, TransformationRule, OutputGenerator, FileInfo

def test_expiry_date_fix():
    """Test the corrected expiry date conversion"""
    print("=== Testing Expiry Date Fix ===")
    
    engine = TransformationEngine()
    
    # Test with the example value from the issue: 1438439400
    test_series = pd.Series(['1438439400', '1430265600', '0', '', 'XX'])
    
    # Apply conversion
    converted = engine._convert_unix_to_date(test_series)
    
    print("Original values:", test_series.tolist())
    print("Converted values:", converted.tolist())
    
    # Expected: 31-JUL-25 (not 01-AUG-15)
    # Using Excel formula: FLOOR(1438439400/60/60/24,1) + DATE(1980,1,1)
    days = int(1438439400 // 86400)  # 16644 days
    expected_date = datetime(1980, 1, 1) + pd.Timedelta(days=days)
    expected_str = expected_date.strftime('%d-%b-%y').upper()
    
    print(f"Expected for 1438439400: {expected_str}")
    print(f"Actual result: {converted.iloc[0]}")
    print()

def test_blank_replacement():
    """Test blank value replacement with hyphens"""
    print("=== Testing Blank Value Replacement ===")
    
    # Create test DataFrame with various blank values
    test_df = pd.DataFrame({
        'col1': ['value1', '', 'value3'],
        'col2': ['value4', None, 'value6'],
        'col3': ['value7', '   ', 'value9']  # whitespace-only
    })
    
    print("Original DataFrame:")
    print(test_df)
    print()
    
    # Create OutputGenerator and test file info
    output_gen = OutputGenerator("test_output")
    file_info = FileInfo(
        sr_number="1",
        file_name="test.txt",
        file_location="test_location"
    )
    
    # This will apply the blank replacement logic
    output_file = output_gen.generate_output_file(test_df, file_info, [], [])
    print(f"Output file created: {output_file}")
    print()

def test_format_enforcement():
    """Test format enforcement (Text/Number conversion)"""
    print("=== Testing Format Enforcement ===")
    
    engine = TransformationEngine()
    
    # Create test data
    test_df = pd.DataFrame({
        'text_col': ['123', '456.78', 'text'],
        'number_col': ['123', '456.78', 'invalid']
    })
    
    # Create transformation rules with format specifications
    rules = [
        TransformationRule("text_col", True, "Text", "-"),
        TransformationRule("number_col", True, "Number", "-")
    ]
    
    print("Original DataFrame:")
    print(test_df)
    print()
    
    # Apply transformations
    df_transformed, dropped, transformed = engine.apply_transformations(test_df, rules)
    
    print("Transformed DataFrame:")
    print(df_transformed)
    print(f"Data types: {df_transformed.dtypes.to_dict()}")
    print()

def test_dynamic_file_path():
    """Test dynamic master file path functionality"""
    print("=== Testing Dynamic File Path ===")
    
    import sys
    import json
    from pathlib import Path
    
    # Test config.json reading
    config_file = Path("config.json")
    if config_file.exists():
        with open(config_file, 'r') as f:
            config = json.load(f)
        print(f"Config file contents: {config}")
        print(f"Master Excel path from config: {config.get('master_excel_path')}")
    else:
        print("No config.json file found")
    
    print(f"Command line arguments: {sys.argv}")
    print()

if __name__ == "__main__":
    print("Testing All Four Fixes")
    print("=" * 50)
    
    test_expiry_date_fix()
    test_blank_replacement()
    test_format_enforcement()
    test_dynamic_file_path()
    
    print("All tests completed!")
