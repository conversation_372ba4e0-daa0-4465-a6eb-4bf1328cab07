# Data Formatting Feature

## Overview

This document describes the new **Data Formatting Feature** that has been added to the existing data processing pipeline. This feature provides comprehensive post-processing formatting capabilities for Excel files based on specifications defined in the master configuration file (`BRD_Automation_RAW.xlsx`).

## Key Features

### 1. **Comprehensive Format Support**
- **Date formatting**: Proper Excel date format with DD-MMM-YY display
- **Text formatting**: Forces text format to prevent Excel auto-conversion
- **Number formatting**: Numeric format with appropriate decimal places
- **Percentage formatting**: Excel percentage format with % symbol
- **Calculated columns**: Automatic addition of calculated columns based on formulas

### 2. **Automatic Format Detection**
- Reads format specifications from all Sr.no. sheets in `BRD_Automation_RAW.xlsx`
- Automatically matches Excel files to appropriate Sr.no. specifications
- Handles missing or unspecified format requirements gracefully

### 3. **Post-Processing Architecture**
- Runs **ONLY after** all main pipeline processing is complete
- Does not interfere with existing pipeline logic
- Completely modular and can be enabled/disabled
- Preserves all existing data while only changing formatting

### 4. **Multiple Usage Modes**
- **Integrated**: Automatically runs after main pipeline processing
- **Standalone**: Can be run independently on existing Excel files
- **Batch processing**: Format all files in a directory
- **Individual file**: Format specific files with known Sr.no.

## Format Types Supported

Based on analysis of the `BRD_Automation_RAW.xlsx` file, the following format types are supported:

| Format Type | Description | Example |
|-------------|-------------|---------|
| `Date` | Excel date format | DD-MMM-YY (e.g., 11-JUL-24) |
| `Text` | Force text format | Prevents auto-conversion |
| `Number` | Numeric format | 123.45 or 123 |
| `Number (%)` | Percentage format | 15.50% |
| `"-"` | No formatting | Leave unchanged |

## Calculated Columns

The formatter can automatically add calculated columns based on formulas specified in the Notes sections of Sr.no. sheets. For example:

- **Long: Final Margin (%)** = Total Margin(%) + Additional Long Margin(%) + Special Long Margin(%) + ELM Long (%) + Delivery Margin(%)
- **Short: Final Margin (%)** = Total Margin(%) + Additional Short Margin(%) + Special Short Margin(%) + ELM Short (%) + Delivery Margin(%)

## File Structure

### New Files Added

1. **`data_formatter.py`** - Main formatting module containing:
   - `FormatMappingReader` - Reads format specs from all Sr.no. sheets
   - `ExcelFormatter` - Applies formatting to Excel files
   - `PostProcessingManager` - Orchestrates the formatting process

2. **`format_excel_files.py`** - Standalone CLI tool for formatting
3. **`test_formatting.py`** - Test script for formatting functionality
4. **`FORMATTING_FEATURE_README.md`** - This documentation

### Modified Files

1. **`data_pipeline.py`** - Enhanced with:
   - Post-processing formatting integration
   - Configuration options for enabling/disabling formatting
   - New methods for formatting existing files

2. **`config.json`** - Added formatting configuration option

## Usage

### 1. Integrated with Main Pipeline

The formatting feature is **enabled by default** and runs automatically after the main pipeline processing:

```bash
python data_pipeline.py
```

To disable formatting, set in `config.json`:
```json
{
  "enable_post_processing_formatting": false
}
```

### 2. Standalone Formatting

Format all Excel files in the output directory:
```bash
python format_excel_files.py BRD_Automation_RAW.xlsx --output-dir output
```

Format a specific file:
```bash
python format_excel_files.py BRD_Automation_RAW.xlsx --file myfile.xlsx --sr-number 5
```

List available format specifications:
```bash
python format_excel_files.py BRD_Automation_RAW.xlsx --list-specs
```

### 3. Programmatic Usage

```python
from data_formatter import format_pipeline_output, format_specific_file

# Format all files in output directory
results = format_pipeline_output("BRD_Automation_RAW.xlsx", "output")

# Format specific file
result = format_specific_file("myfile.xlsx", "5", "BRD_Automation_RAW.xlsx")
```

## Configuration

### Main Pipeline Configuration

In `config.json`:
```json
{
  "master_excel_path": "BRD file Automation.xlsx",
  "output_dir": "output",
  "log_level": "INFO",
  "enable_post_processing_formatting": true
}
```

### Format Specifications

Format specifications are read from `BRD_Automation_RAW.xlsx`:
- Each Sr.no. sheet defines format rules for specific file types
- Columns: Header Name, Required Y/N, Format, Special Action, Example, Notes
- Calculation formulas can be specified in Notes sections

## Error Handling

The formatting feature includes comprehensive error handling:

1. **Graceful degradation**: If formatting fails, the original files remain unchanged
2. **Detailed logging**: All formatting operations are logged
3. **Error reporting**: Detailed error messages for troubleshooting
4. **Status tracking**: Each file's formatting status is tracked and reported

## Output and Reporting

### Formatting Summary Report

After formatting, a summary report is generated:
- `formatting_summary_YYYYMMDD_HHMMSS.xlsx`
- Contains detailed results for each file processed
- Includes statistics on success/failure rates

### Log Files

- `pipeline.log` - Main pipeline log (includes formatting operations)
- `formatting.log` - Standalone formatting operations log

## Testing

Run the test suite to verify formatting functionality:

```bash
python test_formatting.py
```

This tests:
- Format mapping reader functionality
- Excel formatter capabilities
- Post-processing manager operations

## Technical Details

### Architecture

The formatting system uses a modular architecture:

1. **FormatMappingReader**: Reads and parses all Sr.no. sheets
2. **ExcelFormatter**: Applies formatting using openpyxl
3. **PostProcessingManager**: Orchestrates the entire process
4. **Integration Layer**: Connects with the main pipeline

### File Matching Strategy

Files are matched to Sr.no. specifications using:
1. Filename pattern matching (e.g., "sr5", "sr_5")
2. Header column matching with format rules
3. Best-match scoring based on column intersection

### Performance Considerations

- Lazy loading of format specifications
- Efficient Excel file processing using openpyxl
- Minimal memory footprint for large files
- Parallel processing capability (future enhancement)

## Troubleshooting

### Common Issues

1. **"No format rules found"**: Check if the Sr.no. sheet exists in BRD_Automation_RAW.xlsx
2. **"File not found"**: Verify file paths are correct
3. **"Formatting failed"**: Check the log files for detailed error messages

### Debug Mode

Enable verbose logging for troubleshooting:
```bash
python format_excel_files.py BRD_Automation_RAW.xlsx --output-dir output --verbose
```

## Future Enhancements

Potential future improvements:
1. **Custom format types**: Support for additional Excel format types
2. **Conditional formatting**: Apply formatting based on cell values
3. **Chart formatting**: Format charts and graphs in Excel files
4. **Parallel processing**: Process multiple files simultaneously
5. **Format validation**: Validate data against format specifications

## Support

For issues or questions regarding the formatting feature:
1. Check the log files for detailed error messages
2. Run the test suite to verify functionality
3. Use verbose mode for detailed debugging information
4. Review this documentation for usage examples
