#!/usr/bin/env python3
"""
Standalone Excel File Formatter

This script provides a command-line interface for applying post-processing formatting
to Excel files based on specifications in BRD_Automation_RAW.xlsx.

Usage:
    python format_excel_files.py BRD_Automation_RAW.xlsx --output-dir output
    python format_excel_files.py BRD_Automation_RAW.xlsx --file specific_file.xlsx --sr-number 5
"""

import sys
import argparse
import logging
from pathlib import Path
from data_formatter import format_pipeline_output, format_specific_file, PostProcessingManager

def setup_logging(verbose: bool = False):
    """Setup logging configuration"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('formatting.log')
        ]
    )

def format_all_files(master_excel_path: str, output_dir: str, verbose: bool = False):
    """Format all Excel files in the output directory"""
    try:
        print(f"Formatting all Excel files in: {output_dir}")
        print(f"Using format specifications from: {master_excel_path}")
        
        results = format_pipeline_output(master_excel_path, output_dir)
        
        # Display results
        print(f"\nFormatting Results:")
        print(f"Total files processed: {len(results)}")
        
        status_counts = {}
        for result in results:
            status_counts[result.status] = status_counts.get(result.status, 0) + 1
        
        for status, count in status_counts.items():
            print(f"{status}: {count}")
        
        if verbose:
            print(f"\nDetailed Results:")
            for result in results:
                status_symbol = {"Success": "✓", "Partial": "⚠", "Failed": "✗", "Skipped": "○"}
                symbol = status_symbol.get(result.status, "?")
                filename = Path(result.file_path).name
                print(f"{symbol} {filename} (SR {result.sr_number}) - {result.status}")
                
                if result.columns_formatted:
                    print(f"    Formatted columns: {', '.join(result.columns_formatted)}")
                if result.errors:
                    for error in result.errors:
                        print(f"    Error: {error}")
        
        successful_count = len([r for r in results if r.status in ["Success", "Partial"]])
        return successful_count, len(results)
        
    except Exception as e:
        print(f"Error formatting files: {e}")
        return 0, 0

def format_single_file(master_excel_path: str, file_path: str, sr_number: str, verbose: bool = False):
    """Format a single Excel file"""
    try:
        print(f"Formatting file: {file_path}")
        print(f"Using SR number: {sr_number}")
        print(f"Format specifications from: {master_excel_path}")
        
        result = format_specific_file(file_path, sr_number, master_excel_path)
        
        # Display result
        print(f"\nFormatting Result:")
        print(f"File: {Path(result.file_path).name}")
        print(f"Status: {result.status}")
        
        if result.columns_formatted:
            print(f"Columns formatted: {', '.join(result.columns_formatted)}")
        else:
            print("No columns were formatted")
        
        if result.errors:
            print(f"Errors:")
            for error in result.errors:
                print(f"  - {error}")
        
        return result.status in ["Success", "Partial"]
        
    except Exception as e:
        print(f"Error formatting file: {e}")
        return False

def list_format_specifications(master_excel_path: str):
    """List all available format specifications"""
    try:
        from data_formatter import FormatMappingReader
        
        print(f"Reading format specifications from: {master_excel_path}")
        
        reader = FormatMappingReader(master_excel_path)
        format_mappings = reader.read_all_format_specifications()
        
        if not format_mappings:
            print("No format specifications found")
            return
        
        print(f"\nAvailable Format Specifications:")
        print("=" * 50)
        
        for sr_number, rules in format_mappings.items():
            print(f"\nSr.no. {sr_number}:")
            print(f"  Total rules: {len(rules)}")
            
            required_rules = [r for r in rules if r.required]
            print(f"  Required columns: {len(required_rules)}")
            
            format_types = set(r.format_type for r in rules if r.format_type and r.format_type != "-")
            if format_types:
                print(f"  Format types: {', '.join(sorted(format_types))}")
            
            # Show some example columns
            example_columns = [r.header_name for r in required_rules[:5]]
            if example_columns:
                print(f"  Example columns: {', '.join(example_columns)}")
                if len(required_rules) > 5:
                    print(f"    ... and {len(required_rules) - 5} more")
            
            # Show calculation formulas if any
            calc_rules = [r for r in rules if r.calculation_formula]
            if calc_rules:
                print(f"  Calculated columns: {len(calc_rules)}")
                for calc_rule in calc_rules:
                    print(f"    {calc_rule.header_name}: {calc_rule.calculation_formula}")
        
    except Exception as e:
        print(f"Error listing format specifications: {e}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Excel File Formatter - Apply post-processing formatting based on BRD specifications",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Format all files in output directory
  python format_excel_files.py BRD_Automation_RAW.xlsx --output-dir output
  
  # Format a specific file
  python format_excel_files.py BRD_Automation_RAW.xlsx --file myfile.xlsx --sr-number 5
  
  # List available format specifications
  python format_excel_files.py BRD_Automation_RAW.xlsx --list-specs
  
  # Verbose output
  python format_excel_files.py BRD_Automation_RAW.xlsx --output-dir output --verbose
        """
    )
    
    parser.add_argument("master_excel", help="Path to BRD_Automation_RAW.xlsx file")
    parser.add_argument("--output-dir", help="Directory containing Excel files to format")
    parser.add_argument("--file", help="Specific Excel file to format")
    parser.add_argument("--sr-number", help="SR number for specific file formatting")
    parser.add_argument("--list-specs", action="store_true", help="List available format specifications")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    
    # Validate master Excel file
    if not Path(args.master_excel).exists():
        print(f"Error: Master Excel file not found: {args.master_excel}")
        return 1
    
    try:
        if args.list_specs:
            # List format specifications
            list_format_specifications(args.master_excel)
            return 0
            
        elif args.file:
            # Format specific file
            if not args.sr_number:
                print("Error: --sr-number is required when using --file")
                return 1
            
            if not Path(args.file).exists():
                print(f"Error: File not found: {args.file}")
                return 1
            
            success = format_single_file(args.master_excel, args.file, args.sr_number, args.verbose)
            return 0 if success else 1
            
        elif args.output_dir:
            # Format all files in directory
            if not Path(args.output_dir).exists():
                print(f"Error: Output directory not found: {args.output_dir}")
                return 1
            
            successful, total = format_all_files(args.master_excel, args.output_dir, args.verbose)
            
            print(f"\nSummary: {successful}/{total} files formatted successfully")
            return 0 if successful == total else 1
            
        else:
            print("Error: Either --output-dir, --file, or --list-specs must be specified")
            parser.print_help()
            return 1
            
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
